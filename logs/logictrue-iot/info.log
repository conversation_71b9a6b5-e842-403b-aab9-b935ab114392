10:48:31.504 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 158268 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:48:31.507 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:48:32.726 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:48:32.727 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:48:32.727 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:48:32.774 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:48:34.152 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:48:34.282 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:48:34.648 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:48:34.649 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:48:34.997 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:48:35.026 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:48:35.027 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:48:35.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:48:35.110 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:48:35.112 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:48:35.114 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:48:35.116 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:48:35.172 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:48:35.197 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.153 seconds (JVM running for 4.776)
10:49:09.699 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:50:28.253 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
10:50:28.254 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
10:50:28.259 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
10:50:28.260 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:30:00.151 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:30:00.158 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
11:30:00.162 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:30:00.163 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:34:07.464 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:34:07.466 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
11:34:07.469 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:34:07.470 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:23:25.499 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:23:25.500 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:23:25.504 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:23:25.505 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:30:08.186 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:30:08.187 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:30:08.190 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:30:08.192 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:31:33.719 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:31:33.720 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:31:33.723 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:31:33.724 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:59:36.558 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:59:36.560 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:59:36.564 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:59:36.565 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:11:03.271 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:11:03.272 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:11:03.276 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:11:03.277 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:26:01.609 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:26:01.610 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:26:01.614 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:26:01.615 [http-nio-9335-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:30:55.825 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:30:55.826 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:30:55.829 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:30:55.830 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:33:08.154 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:33:08.155 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:33:08.158 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:33:08.159 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:34:26.971 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:34:26.972 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:34:26.975 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:34:26.976 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:38:36.981 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:38:36.982 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:38:36.986 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:38:36.986 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:47:17.997 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:47:17.998 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:47:18.010 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:47:18.010 [http-nio-9335-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:47:38.117 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:47:38.118 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:47:38.121 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:47:38.122 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:48:17.333 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:48:17.333 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:48:17.340 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:48:17.341 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:49:03.366 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:49:03.368 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:49:03.371 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:49:03.372 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:59:06.007 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:59:06.008 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:59:06.012 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:59:06.014 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:59:10.931 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
13:59:10.932 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
13:59:10.935 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
13:59:10.935 [http-nio-9335-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
14:40:43.666 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:40:43.677 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:54:23.932 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 238705 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
16:54:23.935 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:54:25.074 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
16:54:25.075 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:54:25.075 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:54:25.122 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:54:26.346 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:54:26.443 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:54:26.779 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:54:26.780 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:54:27.133 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:54:27.163 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:54:27.164 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:54:27.243 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:54:27.248 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:54:27.251 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:54:27.252 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:54:27.253 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:54:27.255 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:54:27.307 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
16:54:27.325 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 3.924 seconds (JVM running for 4.506)
16:55:46.634 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:55:46.837 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 8
16:55:46.837 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:55:46.838 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 8
16:55:46.844 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:55:46.844 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:55:46.852 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:55:47.058 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:55:47.059 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 6, 总列数: 8
16:55:47.145 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:55:47.146 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:55:47.238 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:55:47.239 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.246 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.248 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.251 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:55:47.254 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
16:55:47.256 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:55:47.260 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:55:47.261 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:55:47.262 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:55:47.263 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
16:55:47.265 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
16:55:47.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:55:47.266 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:55:47.268 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:55:47.409 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3036 bytes
16:55:47.417 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC8%E9%A1%B5_20250901_165547.docx, 大小: 3036 bytes
16:57:09.036 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
16:57:09.036 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:57:09.037 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
16:57:09.041 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:57:09.041 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:57:09.043 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:57:09.045 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:57:09.046 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
16:57:09.050 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:57:09.050 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:57:09.058 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:57:09.059 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.061 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.062 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.065 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:09.066 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
16:57:09.067 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:57:11.876 [http-nio-9335-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
16:57:11.876 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:57:11.876 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
16:57:11.880 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:57:11.881 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:57:11.882 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:57:11.883 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:57:11.883 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
16:57:11.887 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:57:11.888 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:57:11.892 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:57:11.893 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.894 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.896 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.897 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:57:11.898 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
16:57:11.899 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:59:14.059 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:59:14.066 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:59:17.693 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 240633 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
16:59:17.697 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:59:18.964 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
16:59:18.965 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:59:18.965 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:59:19.032 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:59:20.469 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:59:20.603 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:59:21.013 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:59:21.014 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:59:21.415 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:59:21.448 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:59:21.450 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:59:21.551 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:59:21.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:59:21.562 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:59:21.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:59:21.564 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:59:21.566 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:59:21.637 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
16:59:21.671 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.585 seconds (JVM running for 5.153)
16:59:25.237 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:59:25.431 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
16:59:25.432 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
16:59:25.432 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
16:59:25.439 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
16:59:25.440 [http-nio-9335-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
16:59:25.446 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
16:59:25.747 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
16:59:25.747 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
16:59:25.833 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
16:59:25.833 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
16:59:25.934 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
16:59:25.935 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.942 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.947 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.951 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
16:59:25.954 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
16:59:25.955 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:59:25.958 [http-nio-9335-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
16:59:26.340 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.343 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.344 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.346 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
16:59:26.348 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
16:59:26.350 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
16:59:26.354 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
16:59:26.355 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
16:59:26.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
16:59:26.356 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
16:59:26.358 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
16:59:26.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
16:59:26.359 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
16:59:26.361 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
16:59:26.447 [http-nio-9335-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3287 bytes
16:59:26.457 [http-nio-9335-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_165926.docx, 大小: 3287 bytes
17:00:05.304 [http-nio-9335-exec-2] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:00:05.304 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:00:05.304 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:00:05.309 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:00:05.309 [http-nio-9335-exec-2] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:00:05.310 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:00:05.314 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:00:05.314 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:00:05.317 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:00:05.318 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:00:05.325 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:00:05.325 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.328 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.331 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.333 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:05.334 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:00:05.335 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:00:05.336 [http-nio-9335-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:00:05.469 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.470 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.472 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.473 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:05.475 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:00:05.475 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:00:05.476 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:00:05.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:00:05.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:00:05.477 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:00:05.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:00:05.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:00:05.478 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:00:05.479 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:00:05.490 [http-nio-9335-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:00:05.496 [http-nio-9335-exec-2] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170005.docx, 大小: 3286 bytes
17:00:27.583 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:00:27.584 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:00:27.584 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:00:27.589 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:00:27.592 [http-nio-9335-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:00:27.595 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:00:27.597 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:00:27.598 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:00:27.603 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:00:27.603 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:00:27.611 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:00:27.612 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.613 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.614 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.615 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:00:27.617 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:00:27.617 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:00:27.617 [http-nio-9335-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:00:27.716 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.717 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.717 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.718 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:00:27.719 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:00:27.720 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:00:27.720 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:00:27.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:00:27.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:00:27.721 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:00:27.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:00:27.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:00:27.722 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:00:27.723 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:00:27.730 [http-nio-9335-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:00:27.734 [http-nio-9335-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170027.docx, 大小: 3286 bytes
17:02:13.567 [http-nio-9335-exec-5] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:02:13.568 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:02:13.569 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:02:13.579 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:02:13.580 [http-nio-9335-exec-5] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:02:13.582 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:02:13.583 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:02:13.584 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:02:13.586 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:02:13.587 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:02:13.591 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:02:13.592 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.595 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.597 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.598 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:02:13.600 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:02:13.601 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:02:13.602 [http-nio-9335-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:02:13.750 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.752 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.754 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.755 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:02:13.756 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:02:13.758 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:02:13.760 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:02:13.761 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:02:13.762 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:02:13.763 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:02:13.763 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:02:13.764 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:02:13.764 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:02:13.765 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:02:13.778 [http-nio-9335-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:02:13.788 [http-nio-9335-exec-5] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170213.docx, 大小: 3286 bytes
17:02:14.956 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
17:02:14.957 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
17:02:14.961 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
17:02:14.961 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
17:08:09.444 [http-nio-9335-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:08:09.445 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:08:09.445 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:08:09.449 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:08:09.450 [http-nio-9335-exec-4] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:08:09.452 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:08:09.454 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:08:09.454 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:08:09.456 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:08:09.457 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:08:09.460 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:08:09.460 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.461 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.462 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.462 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:08:09.463 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:08:09.463 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:08:09.463 [http-nio-9335-exec-4] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:08:09.553 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.554 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.554 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.555 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:08:09.555 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:08:09.556 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:08:09.557 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:08:09.558 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:08:09.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:08:09.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:08:09.559 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:08:09.560 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:08:09.560 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:08:09.560 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:08:09.569 [http-nio-9335-exec-4] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:08:09.573 [http-nio-9335-exec-4] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170809.docx, 大小: 3286 bytes
17:09:12.657 [http-nio-9335-exec-6] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:09:12.657 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:09:12.657 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:09:12.662 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:09:12.663 [http-nio-9335-exec-6] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:09:12.664 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:09:12.666 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:09:12.666 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:09:12.667 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:09:12.668 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:09:12.671 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:09:12.672 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.673 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.674 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.675 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:09:12.677 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:09:12.678 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:09:12.678 [http-nio-9335-exec-6] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:09:12.786 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.788 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.788 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.789 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:09:12.790 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:09:12.790 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:09:12.791 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:09:12.791 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:09:12.792 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:09:12.792 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:09:12.792 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:09:12.793 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:09:12.794 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:09:12.795 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:09:12.803 [http-nio-9335-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:09:12.807 [http-nio-9335-exec-6] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_170912.docx, 大小: 3286 bytes
17:11:39.333 [http-nio-9335-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,44] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
17:11:39.333 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,33] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
17:11:39.333 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,52] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
17:11:39.340 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,145] - 查询检验记录数据，车辆ID: 0822
17:11:39.341 [http-nio-9335-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,196] - 查询到检验记录数据 4 条
17:11:39.342 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
17:11:39.343 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
17:11:39.344 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 11, 总列数: 8
17:11:39.344 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 880px (13200twips)
17:11:39.345 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
17:11:39.347 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
17:11:39.347 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.347 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.348 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.348 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 30px (450twips)
17:11:39.349 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 151px (2265twips)
17:11:39.349 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:11:39.350 [http-nio-9335-exec-9] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,43] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes/MML2OMML.XSL
17:11:39.426 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.427 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.427 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.428 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 51px (765twips)
17:11:39.429 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
17:11:39.429 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
17:11:39.429 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
17:11:39.430 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
17:11:39.431 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
17:11:39.431 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
17:11:39.431 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
17:11:39.432 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
17:11:39.433 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
17:11:39.434 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
17:11:39.442 [http-nio-9335-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3286 bytes
17:11:39.447 [http-nio-9335-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,72] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250901_171139.docx, 大小: 3286 bytes
