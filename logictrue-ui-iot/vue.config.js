'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

process.env.VUE_APP_MA_VERSION = require('./package.json').version
const port = process.env.port || process.env.npm_config_port || 9981 // 端口

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  runtimeCompiler: true,
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    proxy: {
      [process.env.VUE_APP_BASE_API + '/iot']: {
        target: 'http://127.0.0.1:9335', // 替换为你的 IoT 服务地址
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API + '/iot']: '' // 去掉前缀
        }
      },
      [process.env.VUE_APP_BASE_API + '/word']: {
        target: 'http://127.0.0.1:9335', // 替换为你的 IoT 服务地址
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API + '/word']: '' // 去掉前缀
        }
      },
      [process.env.VUE_APP_BASE_API]: {
        target: 'http://127.0.0.1:9335',
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    disableHostCheck: true
  },
  configureWebpack: {
    name: 'IoT设备检测数据管理系统',
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  }
}
